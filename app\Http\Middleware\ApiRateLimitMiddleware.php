<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class ApiRateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $key = 'api'): ResponseAlias
    {
        $identifier = $this->getIdentifier($request);
        
        // Different rate limits for different endpoints
        $limits = $this->getRateLimits($key);
        
        $executed = RateLimiter::attempt(
            $key . ':' . $identifier,
            $limits['attempts'],
            function () use ($next, $request) {
                return $next($request);
            },
            $limits['decay']
        );

        if (!$executed) {
            return response()->json([
                'success' => false,
                'message' => 'Too many requests. Please try again later.',
                'retry_after' => RateLimiter::availableIn($key . ':' . $identifier),
            ], 429);
        }

        return $executed;
    }

    /**
     * Get the rate limiter identifier for the request.
     */
    protected function getIdentifier(Request $request): string
    {
        // Use user ID if authenticated, otherwise use IP address
        if ($request->user()) {
            return 'user:' . $request->user()->id;
        }

        return 'ip:' . $request->ip();
    }

    /**
     * Get rate limits based on the key.
     */
    protected function getRateLimits(string $key): array
    {
        return match ($key) {
            'auth' => [
                'attempts' => 5,  // 5 attempts
                'decay' => 60,    // per minute
            ],
            'api' => [
                'attempts' => 60, // 60 attempts
                'decay' => 60,    // per minute
            ],
            default => [
                'attempts' => 30, // 30 attempts
                'decay' => 60,    // per minute
            ],
        };
    }
}
