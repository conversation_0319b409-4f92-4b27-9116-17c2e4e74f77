<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Middleware\ApiRateLimitMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// API Version 1 Routes
Route::prefix('v1')->middleware([ApiRateLimitMiddleware::class . ':api'])->group(function () {

    // Public authentication routes with stricter rate limiting
    Route::middleware([ApiRateLimitMiddleware::class . ':auth'])->group(function () {
        Route::post('/register', [AuthController::class, 'register']);
        Route::post('/login', [AuthController::class, 'login']);
    });

    // Protected routes (require authentication)
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);

        // Legacy route for backward compatibility
        Route::get('/user-legacy', function (Request $request) {
            return $request->user();
        });
    });

    // API health check endpoint
    Route::get('/health', function () {
        return response()->json([
            'success' => true,
            'message' => 'Voltifi API is running',
            'version' => '1.0.0',
            'timestamp' => now()->toISOString(),
        ]);
    });
});
