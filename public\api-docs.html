<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voltifi API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        h3 { color: #7f8c8d; }
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        .post { background-color: #28a745; }
        .get { background-color: #007bff; }
        .put { background-color: #ffc107; color: #212529; }
        .delete { background-color: #dc3545; }
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
        }
        .auth-required {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔋 Voltifi API Documentation</h1>
        <p><strong>Version:</strong> 1.0.0</p>
        <p><strong>Base URL:</strong> <code>/api/v1</code></p>
        <p>API for Voltifi - On-demand EV charging and mobile tyre support services</p>

        <h2>Authentication</h2>
        <p>This API uses Bearer token authentication. Include the token in the Authorization header:</p>
        <pre>Authorization: Bearer {your-token}</pre>

        <h2>User Roles</h2>
        <ul>
            <li><strong>supplier</strong> - Service providers (EV charging, tyre services)</li>
            <li><strong>customer</strong> - End users requesting services</li>
        </ul>

        <h2>Response Format</h2>
        <p>All API responses follow this consistent format:</p>
        <pre>{
  "success": true|false,
  "message": "Description of the result",
  "data": { ... },      // Present on success
  "errors": { ... }     // Present on validation errors
}</pre>

        <h2>Endpoints</h2>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /register</h3>
            <p>Register a new user as either a supplier or customer.</p>
            
            <h4>Request Body:</h4>
            <pre>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "role": "customer",           // "supplier" or "customer"
  "password": "password123",
  "password_confirmation": "password123"
}</pre>

            <h4>Response (201):</h4>
            <pre>{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+**********",
      "role": "customer",
      "created_at": "2025-05-25T06:14:23.000000Z"
    },
    "token": "1|abc123...",
    "token_type": "Bearer"
  }
}</pre>
        </div>

        <div class="endpoint">
            <h3><span class="method post">POST</span> /login</h3>
            <p>Authenticate user and receive access token.</p>
            
            <h4>Request Body:</h4>
            <pre>{
  "email": "<EMAIL>",
  "password": "password123"
}</pre>

            <h4>Response (200):</h4>
            <pre>{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+**********",
      "role": "customer",
      "created_at": "2025-05-25T06:14:23.000000Z"
    },
    "token": "1|abc123...",
    "token_type": "Bearer"
  }
}</pre>
        </div>

        <div class="endpoint">
            <div class="auth-required">🔒 Authentication Required</div>
            <h3><span class="method post">POST</span> /logout</h3>
            <p>Logout user and revoke access token.</p>
            
            <h4>Response (200):</h4>
            <pre>{
  "success": true,
  "message": "Logout successful"
}</pre>
        </div>

        <div class="endpoint">
            <div class="auth-required">🔒 Authentication Required</div>
            <h3><span class="method get">GET</span> /user</h3>
            <p>Get authenticated user's profile information.</p>
            
            <h4>Response (200):</h4>
            <pre>{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+**********",
      "role": "customer",
      "created_at": "2025-05-25T06:14:23.000000Z",
      "updated_at": "2025-05-25T06:14:23.000000Z"
    }
  }
}</pre>
        </div>

        <div class="endpoint">
            <h3><span class="method get">GET</span> /health</h3>
            <p>API health check endpoint.</p>
            
            <h4>Response (200):</h4>
            <pre>{
  "success": true,
  "message": "Voltifi API is running",
  "version": "1.0.0",
  "timestamp": "2025-05-25T06:14:23.000000Z"
}</pre>
        </div>

        <h2>Error Responses</h2>
        
        <h3>Validation Error (422)</h3>
        <pre>{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "email": ["The email field is required."],
    "password": ["The password must be at least 8 characters."]
  }
}</pre>

        <h3>Unauthorized (401)</h3>
        <pre>{
  "success": false,
  "message": "Unauthorized"
}</pre>

        <h3>Rate Limit Exceeded (429)</h3>
        <pre>{
  "success": false,
  "message": "Too many requests. Please try again later.",
  "retry_after": 60
}</pre>

        <h2>Rate Limits</h2>
        <ul>
            <li><strong>Authentication endpoints:</strong> 5 requests per minute</li>
            <li><strong>General API endpoints:</strong> 60 requests per minute</li>
        </ul>

        <h2>Testing the API</h2>
        <p>You can test the API using tools like Postman, curl, or any HTTP client. Here's an example using curl:</p>
        
        <h3>Register a new user:</h3>
        <pre>curl -X POST http://your-domain.com/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "role": "customer",
    "password": "password123",
    "password_confirmation": "password123"
  }'</pre>

        <h3>Login:</h3>
        <pre>curl -X POST http://your-domain.com/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'</pre>

        <h3>Get user profile (with token):</h3>
        <pre>curl -X GET http://your-domain.com/api/v1/user \
  -H "Authorization: Bearer {your-token}" \
  -H "Content-Type: application/json"</pre>
    </div>
</body>
</html>
