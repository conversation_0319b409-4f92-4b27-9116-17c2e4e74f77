<?php

/**
 * API Resources Demonstration Script
 * 
 * This script demonstrates the Laravel API Resources implementation
 * in the Voltifi API authentication endpoints.
 * 
 * Run this after starting the Laravel development server to see
 * how the API Resources provide consistent response formatting.
 * 
 * Usage: php demo-api-resources.php
 */

require_once 'vendor/autoload.php';

$baseUrl = 'http://localhost:8000/api/v1';

function makeRequest($method, $url, $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    $defaultHeaders = ['Content-Type: application/json'];
    $headers = array_merge($defaultHeaders, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

function printResourceDemo($title, $response, $resourceType) {
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🔄 $title\n";
    echo "📦 Resource Type: $resourceType\n";
    echo str_repeat("=", 60) . "\n";
    echo "Status: " . $response['status'] . "\n";
    echo "Response Structure:\n";
    echo json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
    
    if (isset($response['body']['data'])) {
        echo "\n📊 Data Structure Analysis:\n";
        analyzeDataStructure($response['body']['data'], $resourceType);
    }
}

function analyzeDataStructure($data, $resourceType) {
    switch ($resourceType) {
        case 'AuthResource':
            echo "✅ AuthResource provides:\n";
            echo "   - user: UserResource transformation\n";
            echo "   - token: Authentication token\n";
            echo "   - token_type: Always 'Bearer'\n";
            
            if (isset($data['user'])) {
                echo "\n🔍 UserResource fields:\n";
                foreach ($data['user'] as $field => $value) {
                    echo "   - $field: " . gettype($value) . "\n";
                }
            }
            break;
            
        case 'UserProfileResource':
            echo "✅ UserProfileResource provides:\n";
            echo "   - user: UserResource transformation\n";
            
            if (isset($data['user'])) {
                echo "\n🔍 UserResource fields:\n";
                foreach ($data['user'] as $field => $value) {
                    echo "   - $field: " . gettype($value) . "\n";
                }
            }
            break;
    }
}

echo "🔋 Voltifi API Resources Demonstration\n";
echo "Showcasing Laravel API Resources for consistent response formatting...\n";

// Demo 1: Register endpoint using AuthResource
echo "\n🎯 Demonstrating API Resources in action...\n";

$customerData = [
    'name' => 'API Resource Demo User',
    'email' => 'demo.user.' . time() . '@example.com',
    'phone' => '+1234567890',
    'role' => 'customer',
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$response = makeRequest('POST', $baseUrl . '/register', $customerData);
printResourceDemo(
    "Registration Endpoint - AuthResource Demo", 
    $response, 
    'AuthResource'
);

if ($response['status'] === 201) {
    $token = $response['body']['data']['token'];
    
    // Demo 2: User profile endpoint using UserProfileResource
    $response = makeRequest('GET', $baseUrl . '/user', null, [
        'Authorization: Bearer ' . $token
    ]);
    printResourceDemo(
        "User Profile Endpoint - UserProfileResource Demo", 
        $response, 
        'UserProfileResource'
    );
    
    // Demo 3: Login endpoint using AuthResource
    $loginData = [
        'email' => $customerData['email'],
        'password' => 'password123'
    ];
    
    $response = makeRequest('POST', $baseUrl . '/login', $loginData);
    printResourceDemo(
        "Login Endpoint - AuthResource Demo", 
        $response, 
        'AuthResource'
    );
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 API Resources Benefits Demonstrated:\n";
echo str_repeat("=", 60) . "\n";
echo "✅ Consistent Data Structure: All user data follows UserResource format\n";
echo "✅ Reusable Components: UserResource used in both AuthResource and UserProfileResource\n";
echo "✅ Type Safety: Resources ensure consistent field types and names\n";
echo "✅ Maintainability: Changes to user data structure only need updates in UserResource\n";
echo "✅ Backward Compatibility: Same JSON structure as before, but now resource-driven\n";

echo "\n📚 Resource Classes Used:\n";
echo "   🔹 UserResource: Base user data transformation\n";
echo "   🔹 AuthResource: Authentication responses (register/login)\n";
echo "   🔹 UserProfileResource: User profile responses\n";

echo "\n🔧 Implementation Details:\n";
echo "   • AuthResource combines UserResource + token data\n";
echo "   • UserProfileResource wraps UserResource in 'user' object\n";
echo "   • ApiResponseTrait automatically handles resource conversion\n";
echo "   • All resources maintain the existing API contract\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "✨ API Resources Refactoring Complete!\n";
echo "The Voltifi API now uses Laravel API Resources for better maintainability\n";
echo "and consistency while preserving 100% backward compatibility.\n";
echo str_repeat("=", 60) . "\n";

?>
