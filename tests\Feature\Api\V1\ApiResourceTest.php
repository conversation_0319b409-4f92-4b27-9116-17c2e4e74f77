<?php

namespace Tests\Feature\Api\V1;

use App\Http\Resources\Api\V1\AuthResource;
use App\Http\Resources\Api\V1\UserProfileResource;
use App\Http\Resources\Api\V1\UserResource;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ApiResourceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_user_resource_structure(): void
    {
        $user = User::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'role' => 'customer',
        ]);

        $resource = new UserResource($user);
        $array = $resource->toArray(request());

        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('name', $array);
        $this->assertArrayHasKey('email', $array);
        $this->assertArrayHasKey('phone', $array);
        $this->assertArrayHasKey('role', $array);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);

        $this->assertEquals($user->id, $array['id']);
        $this->assertEquals('John Doe', $array['name']);
        $this->assertEquals('<EMAIL>', $array['email']);
        $this->assertEquals('+1234567890', $array['phone']);
        $this->assertEquals('customer', $array['role']);
    }

    public function test_auth_resource_structure(): void
    {
        $user = User::factory()->create([
            'role' => 'supplier',
        ]);
        $token = 'test-token-123';

        $resource = new AuthResource($user, $token);
        $array = $resource->toArray(request());

        $this->assertArrayHasKey('user', $array);
        $this->assertArrayHasKey('token', $array);
        $this->assertArrayHasKey('token_type', $array);

        $this->assertEquals('test-token-123', $array['token']);
        $this->assertEquals('Bearer', $array['token_type']);

        // Verify user data structure
        $userData = $array['user'];
        $this->assertArrayHasKey('id', $userData);
        $this->assertArrayHasKey('name', $userData);
        $this->assertArrayHasKey('email', $userData);
        $this->assertArrayHasKey('phone', $userData);
        $this->assertArrayHasKey('role', $userData);
        $this->assertEquals('supplier', $userData['role']);
    }

    public function test_user_profile_resource_structure(): void
    {
        $user = User::factory()->create([
            'role' => 'customer',
        ]);

        $resource = new UserProfileResource($user);
        $array = $resource->toArray(request());

        $this->assertArrayHasKey('user', $array);

        // Verify user data structure
        $userData = $array['user'];
        $this->assertArrayHasKey('id', $userData);
        $this->assertArrayHasKey('name', $userData);
        $this->assertArrayHasKey('email', $userData);
        $this->assertArrayHasKey('phone', $userData);
        $this->assertArrayHasKey('role', $userData);
        $this->assertArrayHasKey('created_at', $userData);
        $this->assertArrayHasKey('updated_at', $userData);
        $this->assertEquals('customer', $userData['role']);
    }

    public function test_register_endpoint_uses_auth_resource(): void
    {
        $userData = [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '+0987654321',
            'role' => 'supplier',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/register', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'phone',
                        'role',
                        'created_at',
                        'updated_at',
                    ],
                    'token',
                    'token_type',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => [
                    'user' => [
                        'name' => 'Jane Smith',
                        'email' => '<EMAIL>',
                        'phone' => '+0987654321',
                        'role' => 'supplier',
                    ],
                    'token_type' => 'Bearer',
                ],
            ]);

        // Verify the token is a string
        $this->assertIsString($response->json('data.token'));
        $this->assertNotEmpty($response->json('data.token'));
    }

    public function test_login_endpoint_uses_auth_resource(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'customer',
        ]);

        $response = $this->postJson('/api/v1/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'phone',
                        'role',
                        'created_at',
                        'updated_at',
                    ],
                    'token',
                    'token_type',
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'role' => 'customer',
                    ],
                    'token_type' => 'Bearer',
                ],
            ]);
    }

    public function test_user_profile_endpoint_uses_user_profile_resource(): void
    {
        $user = User::factory()->create([
            'role' => 'supplier',
        ]);

        $this->actingAs($user, 'sanctum');

        $response = $this->getJson('/api/v1/user');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'user' => [
                        'id',
                        'name',
                        'email',
                        'phone',
                        'role',
                        'created_at',
                        'updated_at',
                    ],
                ],
            ])
            ->assertJson([
                'success' => true,
                'message' => 'User retrieved successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone,
                        'role' => 'supplier',
                    ],
                ],
            ]);
    }

    public function test_backward_compatibility_maintained(): void
    {
        // Test that the response structure is exactly the same as before
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'role' => 'customer',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $response = $this->postJson('/api/v1/register', $userData);

        // Verify the exact structure matches the original implementation
        $responseData = $response->json();
        
        $this->assertTrue($responseData['success']);
        $this->assertEquals('User registered successfully', $responseData['message']);
        $this->assertArrayHasKey('data', $responseData);
        $this->assertArrayHasKey('user', $responseData['data']);
        $this->assertArrayHasKey('token', $responseData['data']);
        $this->assertArrayHasKey('token_type', $responseData['data']);
        $this->assertEquals('Bearer', $responseData['data']['token_type']);

        // Verify user data structure
        $userData = $responseData['data']['user'];
        $this->assertArrayHasKey('id', $userData);
        $this->assertArrayHasKey('name', $userData);
        $this->assertArrayHasKey('email', $userData);
        $this->assertArrayHasKey('phone', $userData);
        $this->assertArrayHasKey('role', $userData);
        $this->assertArrayHasKey('created_at', $userData);
        $this->assertArrayHasKey('updated_at', $userData);
    }
}
