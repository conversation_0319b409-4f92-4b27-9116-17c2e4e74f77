{"openapi": "3.0.0", "info": {"title": "Voltifi API", "description": "API for Voltifi - On-demand EV charging and mobile tyre support services", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "/api/v1", "description": "API V1"}], "paths": {"/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "description": "Register a new user as either a supplier or customer", "operationId": "register", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "email", "phone", "role", "password", "password_confirmation"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+1234567890"}, "role": {"type": "string", "enum": ["supplier", "customer"], "example": "customer"}, "password": {"type": "string", "format": "password", "example": "password123"}, "password_confirmation": {"type": "string", "format": "password", "example": "password123"}}, "type": "object"}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User registered successfully"}, "data": {"properties": {"user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+1234567890"}, "role": {"type": "string", "example": "customer"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "token": {"type": "string", "example": "1|abc123..."}, "token_type": {"type": "string", "example": "Bearer"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}, "errors": {"type": "object"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Registration failed"}}, "type": "object"}}}}}}}, "/login": {"post": {"tags": ["Authentication"], "summary": "Login user", "description": "Authenticate user and return access token", "operationId": "login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}}, "type": "object"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Login successful"}, "data": {"properties": {"user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+1234567890"}, "role": {"type": "string", "example": "customer"}, "created_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "token": {"type": "string", "example": "1|abc123..."}, "token_type": {"type": "string", "example": "Bearer"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Invalid credentials", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> failed"}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/logout": {"post": {"tags": ["Authentication"], "summary": "Logout user", "description": "Logout user and revoke access token", "operationId": "logout", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logout successful"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/user": {"get": {"tags": ["Authentication"], "summary": "Get authenticated user", "description": "Get the authenticated user's profile information", "operationId": "getUser", "responses": {"200": {"description": "User retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User retrieved successfully"}, "data": {"properties": {"user": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "+1234567890"}, "role": {"type": "string", "example": "customer"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Authentication", "description": "Authentication"}]}