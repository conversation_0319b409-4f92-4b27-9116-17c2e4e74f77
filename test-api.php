<?php

/**
 * Simple API Testing Script for Voltifi API
 * 
 * This script demonstrates how to interact with the Voltifi API endpoints.
 * Run this script after starting the Laravel development server.
 * 
 * Usage: php test-api.php
 */

$baseUrl = 'http://localhost:8000/api/v1';

function makeRequest($method, $url, $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    $defaultHeaders = ['Content-Type: application/json'];
    $headers = array_merge($defaultHeaders, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

function printResponse($title, $response) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo $title . "\n";
    echo str_repeat("=", 50) . "\n";
    echo "Status: " . $response['status'] . "\n";
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n";
}

echo "🔋 Voltifi API Testing Script\n";
echo "Testing API endpoints...\n";

// Test 1: Health Check
$response = makeRequest('GET', $baseUrl . '/health');
printResponse("1. Health Check", $response);

// Test 2: Register a new customer
$customerData = [
    'name' => 'John Doe',
    'email' => 'john.doe.' . time() . '@example.com', // Unique email
    'phone' => '+**********',
    'role' => 'customer',
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$response = makeRequest('POST', $baseUrl . '/register', $customerData);
printResponse("2. Register Customer", $response);

if ($response['status'] === 201) {
    $customerToken = $response['body']['data']['token'];
    echo "Customer Token: " . $customerToken . "\n";
    
    // Test 3: Get customer profile
    $response = makeRequest('GET', $baseUrl . '/user', null, [
        'Authorization: Bearer ' . $customerToken
    ]);
    printResponse("3. Get Customer Profile", $response);
    
    // Test 4: Logout customer
    $response = makeRequest('POST', $baseUrl . '/logout', null, [
        'Authorization: Bearer ' . $customerToken
    ]);
    printResponse("4. Logout Customer", $response);
    
    // Test 5: Try to access profile after logout (should fail)
    $response = makeRequest('GET', $baseUrl . '/user', null, [
        'Authorization: Bearer ' . $customerToken
    ]);
    printResponse("5. Access Profile After Logout (Should Fail)", $response);
}

// Test 6: Register a supplier
$supplierData = [
    'name' => 'Jane Smith',
    'email' => 'jane.smith.' . time() . '@example.com', // Unique email
    'phone' => '+0987654321',
    'role' => 'supplier',
    'password' => 'password123',
    'password_confirmation' => 'password123'
];

$response = makeRequest('POST', $baseUrl . '/register', $supplierData);
printResponse("6. Register Supplier", $response);

if ($response['status'] === 201) {
    $supplierEmail = $supplierData['email'];
    
    // Test 7: Login supplier
    $loginData = [
        'email' => $supplierEmail,
        'password' => 'password123'
    ];
    
    $response = makeRequest('POST', $baseUrl . '/login', $loginData);
    printResponse("7. Login Supplier", $response);
    
    if ($response['status'] === 200) {
        $supplierToken = $response['body']['data']['token'];
        
        // Test 8: Get supplier profile
        $response = makeRequest('GET', $baseUrl . '/user', null, [
            'Authorization: Bearer ' . $supplierToken
        ]);
        printResponse("8. Get Supplier Profile", $response);
    }
}

// Test 9: Invalid registration (missing fields)
$invalidData = [
    'name' => 'Invalid User'
    // Missing required fields
];

$response = makeRequest('POST', $baseUrl . '/register', $invalidData);
printResponse("9. Invalid Registration (Should Fail)", $response);

// Test 10: Invalid login
$invalidLogin = [
    'email' => '<EMAIL>',
    'password' => 'wrongpassword'
];

$response = makeRequest('POST', $baseUrl . '/login', $invalidLogin);
printResponse("10. Invalid Login (Should Fail)", $response);

// Test 11: Access protected route without token
$response = makeRequest('GET', $baseUrl . '/user');
printResponse("11. Access Protected Route Without Token (Should Fail)", $response);

echo "\n" . str_repeat("=", 50) . "\n";
echo "✅ API Testing Complete!\n";
echo "Check the responses above to verify API functionality.\n";
echo str_repeat("=", 50) . "\n";

?>
